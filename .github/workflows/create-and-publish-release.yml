name: Publish Python Package and Create Release

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      tag:
        description: 'Tag for release (e.g., v1.0.0)'
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: release
    permissions:
      id-token: write
      contents: write
    steps:
    - uses: actions/checkout@v4
    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: '3.x'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build
    - name: Build package
      run: python -m build
    - name: Publish package to PyPI
      uses: pypa/gh-action-pypi-publish@release/v1
    - name: Create GitHub Release
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        TAG_NAME="${{ github.ref_name }}"
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          TAG_NAME="${{ github.event.inputs.tag }}"
        fi
        gh release create "$TAG_NAME" \
          --title "Release $TAG_NAME" \
          --generate-notes