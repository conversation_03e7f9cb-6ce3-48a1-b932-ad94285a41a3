#!/bin/bash

# ARINC 424 Viewer Code Cleanup Script
# This script performs various code quality checks and cleanup tasks

set -e

PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

echo "=== ARINC 424 Viewer Code Cleanup ==="
echo "Project root: $PROJECT_ROOT"
echo

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check for required tools
echo "Checking for required tools..."
MISSING_TOOLS=()

if ! command_exists clang-format; then
    MISSING_TOOLS+=("clang-format")
fi

if ! command_exists python3; then
    MISSING_TOOLS+=("python3")
fi

if [ ${#MISSING_TOOLS[@]} -ne 0 ]; then
    echo "Missing required tools: ${MISSING_TOOLS[*]}"
    echo "Please install them before running this script."
    exit 1
fi

echo "All required tools found."
echo

# Format C++ code
echo "Formatting C++ code..."
if command_exists clang-format; then
    find src -name "*.cpp" -o -name "*.h" | while read -r file; do
        echo "  Formatting: $file"
        clang-format -i "$file"
    done
    echo "C++ code formatting completed."
else
    echo "clang-format not found, skipping C++ formatting."
fi
echo

# Format Python code
echo "Formatting Python code..."
if command_exists black; then
    black python/ --line-length 88
    echo "Python code formatting completed."
elif command_exists autopep8; then
    find python -name "*.py" | while read -r file; do
        echo "  Formatting: $file"
        autopep8 --in-place --aggressive --aggressive "$file"
    done
    echo "Python code formatting completed."
else
    echo "Neither black nor autopep8 found, skipping Python formatting."
fi
echo

# Check Python code quality
echo "Checking Python code quality..."
if command_exists flake8; then
    flake8 python/ --max-line-length=88 --ignore=E203,W503 || true
elif command_exists pycodestyle; then
    pycodestyle python/ --max-line-length=88 || true
else
    echo "No Python linter found, skipping Python quality check."
fi
echo

# Remove trailing whitespace
echo "Removing trailing whitespace..."
find src python -type f \( -name "*.cpp" -o -name "*.h" -o -name "*.py" \) -exec sed -i 's/[[:space:]]*$//' {} \;
echo "Trailing whitespace removal completed."
echo

# Check for TODO/FIXME comments
echo "Checking for TODO/FIXME comments..."
TODO_COUNT=$(find src python -type f \( -name "*.cpp" -o -name "*.h" -o -name "*.py" \) -exec grep -l "TODO\|FIXME" {} \; | wc -l)
if [ "$TODO_COUNT" -gt 0 ]; then
    echo "Found $TODO_COUNT files with TODO/FIXME comments:"
    find src python -type f \( -name "*.cpp" -o -name "*.h" -o -name "*.py" \) -exec grep -l "TODO\|FIXME" {} \;
else
    echo "No TODO/FIXME comments found."
fi
echo

# Check for large functions (basic check)
echo "Checking for large functions..."
find src -name "*.cpp" | while read -r file; do
    LARGE_FUNCTIONS=$(awk '/^[a-zA-Z_][a-zA-Z0-9_]*::[a-zA-Z_][a-zA-Z0-9_]*\(/ {func=$0; lines=0} /^}$/ {if(lines>50) print FILENAME":"NR":"func" ("lines" lines)"; lines=0} {lines++}' "$file")
    if [ -n "$LARGE_FUNCTIONS" ]; then
        echo "Large functions in $file:"
        echo "$LARGE_FUNCTIONS"
    fi
done
echo

# Generate compilation database if possible
echo "Generating compilation database..."
if [ -d build ]; then
    cd build
    if command_exists cmake; then
        cmake -DCMAKE_EXPORT_COMPILE_COMMANDS=ON ..
        if [ -f compile_commands.json ]; then
            cp compile_commands.json ..
            echo "Compilation database generated."
        fi
    fi
    cd ..
else
    echo "Build directory not found, skipping compilation database generation."
fi
echo

echo "=== Cleanup completed ==="
echo "Summary:"
echo "- C++ code formatted"
echo "- Python code formatted"
echo "- Trailing whitespace removed"
echo "- Code quality checks performed"
echo "- TODO/FIXME comments checked"
echo "- Large functions identified"
echo
echo "Please review any issues found and commit the changes."
