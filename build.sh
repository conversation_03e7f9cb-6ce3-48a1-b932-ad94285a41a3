#!/bin/bash

# Build script for ARINC 424 Viewer

set -e

echo "Building ARINC 424 Viewer..."

# Check if required dependencies are available
echo "Checking dependencies..."

# Check for Qt5
if ! pkg-config --exists Qt5Core Qt5Widgets Qt5WebEngineWidgets; then
    echo "Error: Qt5 development packages not found."
    echo "Please install Qt5 development packages:"
    echo "  Ubuntu/Debian: sudo apt install qtbase5-dev qtwebengine5-dev"
    echo "  Fedora: sudo dnf install qt5-qtbase-devel qt5-qtwebengine-devel"
    echo "  Arch: sudo pacman -S qt5-base qt5-webengine"
    exit 1
fi

# Check for Python development headers
if ! pkg-config --exists python3-embed; then
    echo "Error: Python3 development headers not found."
    echo "Please install Python3 development packages:"
    echo "  Ubuntu/Debian: sudo apt install python3-dev"
    echo "  Fedora: sudo dnf install python3-devel"
    echo "  Arch: sudo pacman -S python"
    exit 1
fi

# Check for CMake
if ! command -v cmake &> /dev/null; then
    echo "Error: CMake not found."
    echo "Please install CMake:"
    echo "  Ubuntu/Debian: sudo apt install cmake"
    echo "  Fedora: sudo dnf install cmake"
    echo "  Arch: sudo pacman -S cmake"
    exit 1
fi

# Check if Python ARINC 424 library is available
echo "Checking Python ARINC 424 library..."
cd "$(dirname "$0")"
if ! python3 -c "import sys; sys.path.append('src'); import arinc424" 2>/dev/null; then
    echo "Error: ARINC 424 Python library not found."
    echo "Please install the library:"
    echo "  pip install -e ."
    exit 1
fi

# Create build directory
mkdir -p build
cd build

# Configure with CMake
echo "Configuring build..."
cmake .. -DCMAKE_BUILD_TYPE=Release

# Build
echo "Building..."
make -j$(nproc)

echo "Build completed successfully!"
echo "Executable: build/arinc424_viewer"
echo ""
echo "To run the application:"
echo "  cd build && ./arinc424_viewer"
echo ""
echo "Make sure to have the flyright.pc file or another ARINC 424 file ready to load."
