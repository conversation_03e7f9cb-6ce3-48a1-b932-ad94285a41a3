#!/usr/bin/env python3
"""
Quick verification that the new features are working
"""

import sys
import os
import json

# Add src directory to path
sys.path.insert(0, 'src')

try:
    import arinc424
    print("✓ ARINC 424 library imported successfully")
except ImportError as e:
    print(f"✗ Failed to import ARINC 424 library: {e}")
    sys.exit(1)

def verify_record_types():
    """Verify that we can detect different record types including SID/STAR"""
    
    print("\nVerifying record type detection...")
    
    record_types = {}
    procedure_count = 0
    
    with open("flyright.pc", "r") as f:
        for line_num, line in enumerate(f, 1):
            if line.startswith("HDR"):
                continue
                
            try:
                record = arinc424.Record()
                if record.read(line):
                    section = record.ident
                    
                    # Count section types
                    if section not in record_types:
                        record_types[section] = 0
                    record_types[section] += 1
                    
                    # Check for procedures
                    json_data = record.json(output=False)
                    data = json.loads(json_data)
                    
                    if "SID/STAR/Approach Identifier" in data and data["SID/STAR/Approach Identifier"].strip():
                        procedure_count += 1
                        
                        # Show first few procedures
                        if procedure_count <= 3:
                            airport = data.get("Airport Identifier", "").strip()
                            proc_id = data.get("SID/STAR/Approach Identifier", "").strip()
                            seq = data.get("Sequence Number", "").strip()
                            fix = data.get("Fix Identifier", "").strip()
                            print(f"  Procedure {procedure_count}: {section} - {airport} {proc_id} seq {seq} fix {fix}")
                
            except Exception as e:
                pass
            
            # Limit for speed
            if line_num >= 50000:
                break
    
    print(f"\nRecord types found:")
    for section, count in sorted(record_types.items()):
        print(f"  {section}: {count}")
    
    print(f"\nTotal procedures found: {procedure_count}")
    
    # Check if we have the expected types
    has_procedures = procedure_count > 0
    has_pe = "PE" in record_types  # STARs
    has_pd = "PD" in record_types  # SIDs
    has_pf = "PF" in record_types  # Approaches
    
    print(f"\nProcedure detection:")
    print(f"  Has procedures: {'✓' if has_procedures else '✗'}")
    print(f"  Has PE (STAR): {'✓' if has_pe else '✗'}")
    print(f"  Has PD (SID): {'✓' if has_pd else '✗'}")
    print(f"  Has PF (Approach): {'✓' if has_pf else '✗'}")
    
    return has_procedures

def main():
    print("Feature Verification")
    print("=" * 30)
    
    if not os.path.exists("flyright.pc"):
        print("✗ flyright.pc file not found")
        return 1
    
    # Verify record types
    procedures_found = verify_record_types()
    
    print("\n" + "=" * 30)
    if procedures_found:
        print("🎉 SUCCESS! The application should now show:")
        print("  • SID, STAR, Approach in the record type filters")
        print("  • Hierarchical filtering: Region → Airport → Record Type")
        print("  • Connected procedure paths on the map")
        print("  • Color-coded procedures (Orange=SID, Green=STAR, Red=Approach)")
        print("\nTo test:")
        print("  1. Open the C++ application")
        print("  2. Load the flyright.pc file")
        print("  3. Check the filter lists for SID/STAR/Approach options")
        print("  4. Select specific airports to see hierarchical filtering")
        print("  5. Look for colored procedure lines on the map")
    else:
        print("❌ No procedures detected - check the implementation")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
