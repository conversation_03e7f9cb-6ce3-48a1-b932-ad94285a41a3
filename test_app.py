#!/usr/bin/env python3
"""
Test script to verify the ARINC 424 parsing functionality
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, 'src')

try:
    import arinc424
    print("✓ ARINC 424 library imported successfully")
except ImportError as e:
    print(f"✗ Failed to import ARINC 424 library: {e}")
    sys.exit(1)

def test_parse_sample_records():
    """Test parsing some sample records from flyright.pc"""
    
    print("\nTesting record parsing...")
    
    # Sample records from the flyright.pc file
    sample_records = [
        "SCAND        6G    CY111380 DU                     6G  N52105227W113525757     029741  407NARRED DEER                      000011204",
        "SCANEAENRT   4574N CY0    IF  B N45000000W074000000                       W0136                                            000331204",
        "SCANP CYOWCYAYOW     010000100Y N45192101W075400217W014000377250YOW CY1800018000CU00Y NAS    OTTAWA MACDONALD-CARTIER INTL     031691204"
    ]
    
    parsed_count = 0
    
    for i, record_line in enumerate(sample_records, 1):
        print(f"\nRecord {i}:")
        print(f"Raw: {record_line[:60]}...")
        
        try:
            record = arinc424.Record()
            if record.read(record_line):
                # Get JSON data
                json_data = record.json(output=False)
                print(f"✓ Parsed successfully")
                print(f"  Section: {record.ident}")
                print(f"  Definition: {record.definition.name}")
                
                # Parse JSON to extract key info
                import json
                data = json.loads(json_data)
                
                if "Customer / Area Code" in data:
                    print(f"  Region: {data['Customer / Area Code']}")
                
                # Look for coordinates
                coord_fields = ["DME Latitude", "DME Longitude", "VOR Latitude", "VOR Longitude", 
                               "Waypoint Latitude", "Waypoint Longitude", "Airport Reference Pt. Latitude", "Airport Reference Pt. Longitude"]
                
                for lat_field in ["DME Latitude", "VOR Latitude", "Waypoint Latitude", "Airport Reference Pt. Latitude"]:
                    if lat_field in data and data[lat_field].strip():
                        lon_field = lat_field.replace("Latitude", "Longitude")
                        if lon_field in data and data[lon_field].strip():
                            print(f"  Coordinates: {data[lat_field]}, {data[lon_field]}")
                            break
                
                parsed_count += 1
            else:
                print(f"✗ Failed to parse")
                
        except Exception as e:
            print(f"✗ Error parsing: {e}")
    
    print(f"\nSummary: {parsed_count}/{len(sample_records)} records parsed successfully")
    return parsed_count == len(sample_records)

def test_file_parsing():
    """Test parsing the actual flyright.pc file"""
    
    print("\nTesting file parsing...")
    
    if not os.path.exists("flyright.pc"):
        print("✗ flyright.pc file not found")
        return False
    
    try:
        record_count = 0
        parsed_count = 0
        
        with open("flyright.pc", "r") as f:
            for line_num, line in enumerate(f, 1):
                if line.startswith("HDR"):
                    continue
                    
                record_count += 1
                
                try:
                    record = arinc424.Record()
                    if record.read(line):
                        parsed_count += 1
                except:
                    pass
                
                # Test first 100 records only
                if record_count >= 100:
                    break
        
        print(f"✓ Tested {record_count} records")
        print(f"✓ Successfully parsed {parsed_count} records")
        print(f"✓ Success rate: {parsed_count/record_count*100:.1f}%")
        
        return parsed_count > 0
        
    except Exception as e:
        print(f"✗ Error reading file: {e}")
        return False

def main():
    print("ARINC 424 Viewer - Testing")
    print("=" * 40)
    
    # Test 1: Sample record parsing
    test1_passed = test_parse_sample_records()
    
    # Test 2: File parsing
    test2_passed = test_file_parsing()
    
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"Sample record parsing: {'✓ PASS' if test1_passed else '✗ FAIL'}")
    print(f"File parsing: {'✓ PASS' if test2_passed else '✗ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All tests passed! The application should work correctly.")
        print("\nTo run the C++ application:")
        print("  cd build && ./arinc424_viewer")
    else:
        print("\n❌ Some tests failed. Check the ARINC 424 library installation.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
