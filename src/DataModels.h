/**
 * @file DataModels.h
 * @brief Data structures and models for ARINC 424 aviation data
 *
 * This file contains the core data structures used to represent and manage
 * ARINC 424 aviation navigation data including records, procedures, and filtering.
 */

#pragma once

#include <QString>
#include <QVector>
#include <QVariantMap>
#include <QGeoCoordinate>
#include <QSet>
#include <QHash>

/**
 * @enum RecordType
 * @brief Enumeration of ARINC 424 record types
 *
 * This enum represents the different types of aviation navigation records
 * that can be found in ARINC 424 data files.
 */
enum class RecordType {
    HEADER,                  ///< Header Record (HDR)
    GRID_MORA,               ///< Grid MORA (AS)
    VHF_NAVAID,              ///< VHF Navigation Aid (D)
    NDB_NAVAID,              ///< NDB Navigation Aid (DB)
    WAYPOINT,                ///< Waypoint (EA)
    AIRWAYS_MARKER,          ///< Airways Marker (EM)
    HOLDING_PATTERN,         ///< Holding Pattern (EP)
    ENROUTE_AIRWAYS,         ///< Enroute Airways (ER)
    PREFERRED_ROUTE,         ///< Preferred Route (ET)
    ENROUTE_AIRWAYS_RESTRICTION, ///< Enroute Airways Restriction (EU)
    ENROUTE_COMMUNICATIONS,  ///< Enroute Communications (EV)
    HELIPORT,                ///< Heliport (HA)
    HELIPORT_TERMINAL_WAYPOINT, ///< Heliport Terminal Waypoint (HC)
    HELIPORT_SID,            ///< Heliport SID (HD)
    HELIPORT_STAR,           ///< Heliport STAR (HE)
    HELIPORT_APPROACH,       ///< Heliport Approach (HF)
    HELIPORT_TAA,            ///< Heliport TAA (HK)
    HELIPORT_MSA,            ///< Heliport MSA (HS)
    HELIPORT_COMMUNICATIONS, ///< Heliport Communications (HV)
    AIRPORT,                 ///< Airport (PA)
    AIRPORT_GATE,            ///< Airport Gate (PB)
    AIRPORT_NDB,             ///< Airport NDB (PN)
    TAA,                     ///< Terminal Arrival Area (PK)
    MLS,                     ///< Microwave Landing System (PL)
    FLIGHT_PLANNING,         ///< Flight Planning (PR)
    SID,                     ///< Standard Instrument Departure (PD)
    STAR,                    ///< Standard Terminal Arrival Route (PE)
    APPROACH,                ///< Approach Procedure (PF)
    COMPANY_ROUTE,           ///< Company Route (R)
    ALTERNATE,               ///< Alternate (RA)
    CRUISING_TABLES,         ///< Cruising Tables (TC)
    GEO_REFERENCE_TABLE,     ///< Geographical Reference Table (TG)
    CONTROLLED_AIRSPACE,     ///< Controlled Airspace (UC)
    FIR_UIR,                 ///< FIR/UIR (UF)
    RESTRICTIVE_AIRSPACE,    ///< Restrictive Airspace (UR)
    UNKNOWN                  ///< Unknown or unrecognized record type
};

// Hash function for RecordType enum to use in QHash
inline uint qHash(RecordType type, uint seed = 0) {
    return qHash(static_cast<int>(type), seed);
}

struct ArincRecord {
    RecordType type;
    QString sectionCode;
    QString customerAreaCode;  // Region (CAN, USA, etc.)
    QString identifier;
    QString name;
    QGeoCoordinate coordinate;
    QVariantMap additionalData;
    
    // For approaches/departures - sequence information
    int sequenceNumber = -1;
    QString procedureIdentifier;
    QString routeType;
    
    // Constructor
    ArincRecord() : type(RecordType::UNKNOWN) {}
    
    bool isValid() const {
        return type != RecordType::UNKNOWN;
    }
    
    QString getDisplayName() const {
        if (!name.isEmpty()) return name;
        if (!identifier.isEmpty()) return identifier;
        return "Unknown";
    }
};

struct ProcedurePath {
    QString procedureId;
    QString airportCode;
    RecordType procedureType;
    QVector<ArincRecord> waypoints;
    
    bool isValid() const {
        return !procedureId.isEmpty() && waypoints.size() > 1;
    }
};

class ArincDataModel {
public:
    ArincDataModel();
    
    // Data management
    void addRecord(const ArincRecord& record);
    void clearData();
    void finalizeProcedures(); // Call after all records are loaded
    
    // Filtering
    QVector<ArincRecord> getFilteredRecords(
        const QStringList& regions = {},
        const QStringList& recordTypes = {},
        const QStringList& airports = {}
    ) const;
    
    QVector<ProcedurePath> getFilteredProcedures(
        const QStringList& airports = {},
        const QStringList& procedureTypes = {}
    ) const;
    
    // Getters for filter options (hierarchical)
    QStringList getAvailableRegions() const;
    QStringList getAvailableAirports(const QStringList& selectedRegions = {}) const;
    QStringList getAvailableRecordTypes(const QStringList& selectedAirports = {}) const;
    
    // Statistics
    int getTotalRecordCount() const { return m_records.size(); }
    int getRecordCount(RecordType type) const;
    
private:
    QVector<ArincRecord> m_records;
    QVector<ProcedurePath> m_procedures;

    // Performance optimization: cached filter options
    mutable QSet<QString> m_cachedRegions;
    mutable QHash<QString, QSet<QString>> m_cachedAirportsByRegion;
    mutable QHash<QString, QSet<QString>> m_cachedTypesByAirport;
    mutable bool m_cacheValid = false;

    // Helper methods
    void buildProcedurePaths();
    void invalidateCache() const;
    void buildCache() const;
    QString recordTypeToString(RecordType type) const;
    QString sectionCodeToDisplayName(const QString& sectionCode) const;
};

// Helper function
RecordType parseRecordType(const QString& sectionCode);
