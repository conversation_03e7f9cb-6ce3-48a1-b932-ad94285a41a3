#include "DataModels.h"
#include <QDebug>
#include <QRegularExpression>
#include <algorithm>

ArincDataModel::ArincDataModel() {
}

void ArincDataModel::addRecord(const ArincRecord& record) {
    if (record.isValid()) {
        m_records.append(record);
        invalidateCache(); // Cache needs to be rebuilt

        // If this is a procedure record, trigger procedure path building
        if (record.type == RecordType::SID || record.type == RecordType::STAR || record.type == RecordType::APPROACH) {
            // We'll rebuild all procedures when data loading is complete
            // For now, just add the record
        }
    }
}

void ArincDataModel::clearData() {
    m_records.clear();
    m_procedures.clear();
    invalidateCache();
}

void ArincDataModel::finalizeProcedures() {
    buildProcedurePaths();
}

QVector<ArincRecord> ArincDataModel::getFilteredRecords(
    const QStringList& regions,
    const QStringList& recordTypes,
    const QStringList& airports) const {

    QVector<ArincRecord> filtered;

    // If no filters are selected, return all valid records with coordinates
    bool hasAnyFilters = !regions.isEmpty() || !recordTypes.isEmpty() || !airports.isEmpty();



    for (const auto& record : m_records) {
        // Skip records without valid coordinates
        if (!record.coordinate.isValid()) {
            continue;
        }

        // If no filters are applied, include all records with valid coordinates
        if (!hasAnyFilters) {
            filtered.append(record);
            continue;
        }

        // Apply filters only if they are specified
        bool passesFilters = true;

        // Filter by region (only if regions are specified)
        if (!regions.isEmpty() && !regions.contains(record.customerAreaCode)) {
            passesFilters = false;
        }

        // Filter by record type (only if record types are specified)
        if (passesFilters && !recordTypes.isEmpty()) {
            QString sectionCode = record.sectionCode.trimmed();
            QString typeStr = sectionCodeToDisplayName(sectionCode);
            if (!recordTypes.contains(typeStr)) {
                passesFilters = false;
            }
        }

        // Filter by airport (only if airports are specified)
        if (passesFilters && !airports.isEmpty()) {
            bool matchesAirport = false;
            for (const QString& airport : airports) {
                // Check multiple possible fields for airport association
                QString airportId1 = record.additionalData.value("Airport Identifier").toString();
                QString airportId2 = record.additionalData.value("Airport ICAO Identifier").toString();
                QString airportId3 = record.additionalData.value("Airport ICAO Ident").toString();
                QString airportId4 = record.additionalData.value("ICAO Code").toString();

                // For terminal area records, check if identifier starts with airport code
                bool startsWithAirport = record.identifier.startsWith(airport, Qt::CaseInsensitive);

                // For procedure records, check procedure identifier
                bool procedureMatch = record.procedureIdentifier.contains(airport, Qt::CaseInsensitive);

                if (record.identifier.contains(airport, Qt::CaseInsensitive) ||
                    airportId1.contains(airport, Qt::CaseInsensitive) ||
                    airportId2.contains(airport, Qt::CaseInsensitive) ||
                    airportId3.contains(airport, Qt::CaseInsensitive) ||
                    airportId4.contains(airport, Qt::CaseInsensitive) ||
                    startsWithAirport ||
                    procedureMatch ||
                    (record.type == RecordType::AIRPORT && record.identifier == airport)) {
                    matchesAirport = true;
                    break;
                }
            }
            if (!matchesAirport) {
                passesFilters = false;
            }
        }

        if (passesFilters) {
            filtered.append(record);
        }
    }



    return filtered;
}

QVector<ProcedurePath> ArincDataModel::getFilteredProcedures(
    const QStringList& airports,
    const QStringList& procedureTypes) const {
    
    QVector<ProcedurePath> filtered;
    
    for (const auto& procedure : m_procedures) {
        // Filter by airport
        if (!airports.isEmpty() && !airports.contains(procedure.airportCode, Qt::CaseInsensitive)) {
            continue;
        }
        
        // Filter by procedure type
        if (!procedureTypes.isEmpty()) {
            QString typeStr = recordTypeToString(procedure.procedureType);
            if (!procedureTypes.contains(typeStr)) {
                continue;
            }
        }
        
        filtered.append(procedure);
    }
    
    return filtered;
}

QStringList ArincDataModel::getAvailableRegions() const {
    if (!m_cacheValid) {
        buildCache();
    }

    QStringList regions = m_cachedRegions.values();
    regions.sort();
    return regions;
}

QStringList ArincDataModel::getAvailableRecordTypes(const QStringList& selectedAirports) const {
    if (!m_cacheValid) {
        buildCache();
    }

    QSet<QString> types;

    if (selectedAirports.isEmpty()) {
        // Return all types from all airports
        for (const auto& airportTypes : m_cachedTypesByAirport) {
            types.unite(airportTypes);
        }
    } else {
        // Return types only from selected airports
        for (const QString& airport : selectedAirports) {
            if (m_cachedTypesByAirport.contains(airport)) {
                types.unite(m_cachedTypesByAirport[airport]);
            }
        }
    }

    QStringList result = types.values();
    result.sort();
    return result;
}

QStringList ArincDataModel::getAvailableAirports(const QStringList& selectedRegions) const {
    if (!m_cacheValid) {
        buildCache();
    }

    QSet<QString> airports;

    if (selectedRegions.isEmpty()) {
        // Return all airports from all regions
        for (const auto& regionAirports : m_cachedAirportsByRegion) {
            airports.unite(regionAirports);
        }
    } else {
        // Return airports only from selected regions
        for (const QString& region : selectedRegions) {
            if (m_cachedAirportsByRegion.contains(region)) {
                airports.unite(m_cachedAirportsByRegion[region]);
            }
        }
    }

    QStringList result = airports.values();
    result.sort();
    return result;
}

int ArincDataModel::getRecordCount(RecordType type) const {
    return std::count_if(m_records.begin(), m_records.end(),
                        [type](const ArincRecord& record) {
                            return record.type == type;
                        });
}

RecordType parseRecordType(const QString& sectionCode) {
    // Handle Grid MORA records
    if (sectionCode == "AS") return RecordType::GRID_MORA;

    // Handle VHF Navaid records (D followed by space)
    if (sectionCode == "D " || sectionCode == "D") return RecordType::VHF_NAVAID;

    // Handle NDB Navaid records
    if (sectionCode == "DB") return RecordType::NDB_NAVAID;

    // Handle Enroute records
    if (sectionCode == "E " || sectionCode == "E") return RecordType::PREFERRED_ROUTE; // ET records with space at position 12
    if (sectionCode == "EA") return RecordType::WAYPOINT;
    if (sectionCode == "EM") return RecordType::AIRWAYS_MARKER;
    if (sectionCode == "EP") return RecordType::HOLDING_PATTERN;
    if (sectionCode == "ER") return RecordType::ENROUTE_AIRWAYS;
    if (sectionCode == "ET") return RecordType::PREFERRED_ROUTE;
    if (sectionCode == "EU") return RecordType::ENROUTE_AIRWAYS_RESTRICTION;
    if (sectionCode == "EV") return RecordType::ENROUTE_COMMUNICATIONS;

    // Handle Heliport records
    if (sectionCode == "HA") return RecordType::HELIPORT;
    if (sectionCode == "HC") return RecordType::HELIPORT_TERMINAL_WAYPOINT;
    if (sectionCode == "HD") return RecordType::HELIPORT_SID;
    if (sectionCode == "HE") return RecordType::HELIPORT_STAR;
    if (sectionCode == "HF") return RecordType::HELIPORT_APPROACH;
    if (sectionCode == "HK") return RecordType::HELIPORT_TAA;
    if (sectionCode == "HS") return RecordType::HELIPORT_MSA;
    if (sectionCode == "HV") return RecordType::HELIPORT_COMMUNICATIONS;

    // Handle Terminal records with subsection codes (PA, PC, PD, etc.)
    if (sectionCode == "PA") return RecordType::AIRPORT;
    if (sectionCode == "PB") return RecordType::AIRPORT_GATE;
    if (sectionCode == "PC") return RecordType::WAYPOINT; // Terminal Waypoint
    if (sectionCode == "PD") return RecordType::SID;
    if (sectionCode == "PE") return RecordType::STAR;
    if (sectionCode == "PF") return RecordType::APPROACH;
    if (sectionCode == "PG") return RecordType::AIRPORT; // Runway (treat as airport-related)
    if (sectionCode == "PI") return RecordType::VHF_NAVAID; // Localizer/Glideslope
    if (sectionCode == "PK") return RecordType::TAA;
    if (sectionCode == "PL") return RecordType::MLS;
    if (sectionCode == "PM") return RecordType::VHF_NAVAID; // Localizer Marker
    if (sectionCode == "PN") return RecordType::AIRPORT_NDB;
    if (sectionCode == "PP") return RecordType::WAYPOINT; // Path Point
    if (sectionCode == "PR") return RecordType::FLIGHT_PLANNING;
    if (sectionCode == "PS") return RecordType::AIRPORT; // MSA (treat as airport-related)
    if (sectionCode == "PT") return RecordType::AIRPORT; // Terminal Procedures
    if (sectionCode == "PV") return RecordType::AIRPORT; // Airport Communication

    // Handle Company Route records
    if (sectionCode == "R " || sectionCode == "R") return RecordType::COMPANY_ROUTE;
    if (sectionCode == "RA") return RecordType::ALTERNATE;

    // Handle Table records
    if (sectionCode == "TC") return RecordType::CRUISING_TABLES;
    if (sectionCode == "TG") return RecordType::GEO_REFERENCE_TABLE;

    // Handle Airspace records
    if (sectionCode == "UC") return RecordType::CONTROLLED_AIRSPACE;
    if (sectionCode == "UF") return RecordType::FIR_UIR;
    if (sectionCode == "UR") return RecordType::RESTRICTIVE_AIRSPACE;

    return RecordType::UNKNOWN;
}

QString ArincDataModel::recordTypeToString(RecordType type) const {
    switch (type) {
        case RecordType::HEADER: return "Header";
        case RecordType::GRID_MORA: return "Grid MORA";
        case RecordType::VHF_NAVAID: return "VHF Navaid";
        case RecordType::NDB_NAVAID: return "NDB Navaid";
        case RecordType::WAYPOINT: return "Waypoint";
        case RecordType::AIRWAYS_MARKER: return "Airways Marker";
        case RecordType::HOLDING_PATTERN: return "Holding Pattern";
        case RecordType::ENROUTE_AIRWAYS: return "Enroute Airways";
        case RecordType::PREFERRED_ROUTE: return "Preferred Route";
        case RecordType::ENROUTE_AIRWAYS_RESTRICTION: return "Enroute Airways Restriction";
        case RecordType::ENROUTE_COMMUNICATIONS: return "Enroute Communications";
        case RecordType::HELIPORT: return "Heliport";
        case RecordType::HELIPORT_TERMINAL_WAYPOINT: return "Heliport Terminal Waypoint";
        case RecordType::HELIPORT_SID: return "Heliport SID";
        case RecordType::HELIPORT_STAR: return "Heliport STAR";
        case RecordType::HELIPORT_APPROACH: return "Heliport Approach";
        case RecordType::HELIPORT_TAA: return "Heliport TAA";
        case RecordType::HELIPORT_MSA: return "Heliport MSA";
        case RecordType::HELIPORT_COMMUNICATIONS: return "Heliport Communications";
        case RecordType::AIRPORT: return "Airport";
        case RecordType::AIRPORT_GATE: return "Airport Gate";
        case RecordType::AIRPORT_NDB: return "Airport NDB";
        case RecordType::TAA: return "Terminal Arrival Area";
        case RecordType::MLS: return "Microwave Landing System";
        case RecordType::FLIGHT_PLANNING: return "Flight Planning";
        case RecordType::SID: return "SID";
        case RecordType::STAR: return "STAR";
        case RecordType::APPROACH: return "Approach";
        case RecordType::COMPANY_ROUTE: return "Company Route";
        case RecordType::ALTERNATE: return "Alternate";
        case RecordType::CRUISING_TABLES: return "Cruising Tables";
        case RecordType::GEO_REFERENCE_TABLE: return "Geographical Reference Table";
        case RecordType::CONTROLLED_AIRSPACE: return "Controlled Airspace";
        case RecordType::FIR_UIR: return "FIR/UIR";
        case RecordType::RESTRICTIVE_AIRSPACE: return "Restrictive Airspace";
        default: return "Unknown";
    }
}

QString ArincDataModel::sectionCodeToDisplayName(const QString& sectionCode) const {
    if (sectionCode == "D ") return "VHF Navaid (D)";
    if (sectionCode == "DB") return "NDB Navaid (DB)";
    if (sectionCode == "EA") return "Enroute Waypoint (EA)";
    if (sectionCode == "EM") return "Airways Marker (EM)";
    if (sectionCode == "EP") return "Holding Pattern (EP)";
    if (sectionCode == "ER") return "Enroute Airways (ER)";
    if (sectionCode == "ET") return "Preferred Route (ET)";
    if (sectionCode == "PA") return "Airport (PA)";
    if (sectionCode == "PC") return "Terminal Waypoint (PC)";
    if (sectionCode == "PD") return "SID (PD)";
    if (sectionCode == "PE") return "STAR (PE)";
    if (sectionCode == "PF") return "Approach (PF)";
    if (sectionCode == "PG") return "Runway (PG)";
    if (sectionCode == "PI") return "Localizer/Glideslope (PI)";
    if (sectionCode == "PM") return "Localizer Marker (PM)";
    if (sectionCode == "PN") return "Airport NDB (PN)";
    if (sectionCode == "PP") return "Path Point (PP)";
    if (sectionCode == "PS") return "MSA (PS)";
    if (sectionCode == "PT") return "Terminal Procedures (PT)";
    if (sectionCode == "PV") return "Airport Communication (PV)";
    if (sectionCode == "UC") return "Controlled Airspace (UC)";
    if (sectionCode == "UR") return "Restrictive Airspace (UR)";
    return QString("Unknown (%1)").arg(sectionCode);
}

void ArincDataModel::buildProcedurePaths() {
    m_procedures.clear();

    qDebug() << "Building procedure paths...";

    // Group procedure records by airport, procedure ID, and transition
    QMap<QString, QVector<ArincRecord>> procedureGroups;

    for (const auto& record : m_records) {
        if (record.type == RecordType::SID || record.type == RecordType::STAR || record.type == RecordType::APPROACH) {
            QString airportId = record.additionalData.value("Airport Identifier").toString();
            QString procedureId = record.procedureIdentifier;
            QString transitionId = record.additionalData.value("Transition Identifier").toString();

            if (!airportId.isEmpty() && !procedureId.isEmpty()) {
                QString groupKey = QString("%1_%2_%3").arg(airportId, procedureId, transitionId);
                procedureGroups[groupKey].append(record);
            }
        }
    }

    qDebug() << "Found" << procedureGroups.size() << "procedure groups";

    // Build procedure paths from grouped records
    for (auto it = procedureGroups.begin(); it != procedureGroups.end(); ++it) {
        QVector<ArincRecord> records = it.value();

        if (records.size() < 2) continue; // Need at least 2 waypoints for a path

        // Filter out records with invalid sequence numbers and sort
        QVector<ArincRecord> validRecords;
        for (const auto& record : records) {
            if (record.sequenceNumber >= 0) {
                validRecords.append(record);
            }
        }

        if (validRecords.size() < 2) continue; // Need at least 2 valid waypoints

        // Sort by sequence number - now safe since all records have valid sequence numbers
        std::sort(validRecords.begin(), validRecords.end(), [](const ArincRecord& a, const ArincRecord& b) {
            return a.sequenceNumber < b.sequenceNumber;
        });

        // Create procedure path
        ProcedurePath procedure;
        procedure.procedureId = validRecords.first().procedureIdentifier;
        procedure.airportCode = validRecords.first().additionalData.value("Airport Identifier").toString();
        procedure.procedureType = validRecords.first().type;
        procedure.waypoints = validRecords;

        if (procedure.isValid()) {
            m_procedures.append(procedure);
        }
    }

    qDebug() << "Built" << m_procedures.size() << "procedure paths";
}

void ArincDataModel::invalidateCache() const {
    m_cacheValid = false;
    m_cachedRegions.clear();
    m_cachedAirportsByRegion.clear();
    m_cachedTypesByAirport.clear();
}

void ArincDataModel::buildCache() const {
    if (m_cacheValid) return;

    invalidateCache(); // Clear existing cache

    int airportRecordsFound = 0;
    QSet<QString> foundAirports;

    for (const auto& record : m_records) {
        // Cache regions
        QString region = record.customerAreaCode.trimmed();
        if (!region.isEmpty()) {
            m_cachedRegions.insert(region);
        }

        // Cache airports by region - check multiple possible fields
        QString airportId;
        if (record.additionalData.contains("Airport Identifier")) {
            airportId = record.additionalData.value("Airport Identifier").toString().trimmed();
        } else if (record.additionalData.contains("Airport ICAO Identifier")) {
            airportId = record.additionalData.value("Airport ICAO Identifier").toString().trimmed();
        } else if (record.additionalData.contains("Airport ICAO Ident")) {
            airportId = record.additionalData.value("Airport ICAO Ident").toString().trimmed();
        } else if (record.type == RecordType::AIRPORT && !record.identifier.isEmpty()) {
            airportId = record.identifier.trimmed();
        } else if (!record.procedureIdentifier.isEmpty() && record.procedureIdentifier.length() >= 4) {
            // For procedure records, try to extract airport code from procedure identifier
            // Many procedures have format like "KJFK01L" where KJFK is the airport
            QString procId = record.procedureIdentifier.trimmed();
            if (procId.length() >= 4 && procId.startsWith("K")) {
                airportId = procId.left(4); // Extract first 4 characters as potential airport code
            }
        } else if (record.identifier.length() >= 4 && record.identifier.startsWith("K")) {
            // For US airports, try to extract airport code from identifier if it starts with K
            airportId = record.identifier.left(4);
        }

        if (!airportId.isEmpty() && !region.isEmpty()) {
            m_cachedAirportsByRegion[region].insert(airportId);
            foundAirports.insert(airportId);
            airportRecordsFound++;
        }

        // Cache record types by airport
        if (!airportId.isEmpty()) {
            QString typeStr = sectionCodeToDisplayName(record.sectionCode.trimmed());
            m_cachedTypesByAirport[airportId].insert(typeStr);
        }
    }



    m_cacheValid = true;
}
