#include <QApplication>
#include <QStyleFactory>
#include <QDir>
#include <QDebug>
#include <QCommandLineParser>
#include <QCommandLineOption>
#include "MainWindow.h"

int main(int argc, char *argv[]) {
    QApplication app(argc, argv);

    // Set application properties
    app.setApplicationName("ARINC 424 Viewer");
    app.setApplicationVersion("1.0");
    app.setOrganizationName("ARINC Viewer");

    // Setup command line parser
    QCommandLineParser parser;
    parser.setApplicationDescription("ARINC 424 aviation data viewer");
    parser.addHelpOption();
    parser.addVersionOption();

    // Add load file option
    QCommandLineOption loadFileOption(QStringList() << "l" << "load",
        "Load ARINC 424 file on startup", "file");
    parser.addOption(loadFileOption);

    // Process command line arguments
    parser.process(app);
    
    // Set a modern style
    app.setStyle(QStyleFactory::create("Fusion"));
    
    // Apply dark theme
    QPalette darkPalette;
    darkPalette.setColor(QPalette::Window, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::WindowText, Qt::white);
    darkPalette.setColor(QPalette::Base, QColor(25, 25, 25));
    darkPalette.setColor(QPalette::AlternateBase, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ToolTipBase, Qt::white);
    darkPalette.setColor(QPalette::ToolTipText, Qt::white);
    darkPalette.setColor(QPalette::Text, Qt::white);
    darkPalette.setColor(QPalette::Button, QColor(53, 53, 53));
    darkPalette.setColor(QPalette::ButtonText, Qt::white);
    darkPalette.setColor(QPalette::BrightText, Qt::red);
    darkPalette.setColor(QPalette::Link, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::Highlight, QColor(42, 130, 218));
    darkPalette.setColor(QPalette::HighlightedText, Qt::black);
    app.setPalette(darkPalette);
    
    // Get the file to load if specified
    QString fileToLoad;
    if (parser.isSet(loadFileOption)) {
        fileToLoad = parser.value(loadFileOption);
    }

    // Create and show main window
    MainWindow window;
    window.show();

    // Load file if specified
    if (!fileToLoad.isEmpty()) {
        window.loadFile(fileToLoad);
    }

    return app.exec();
}
