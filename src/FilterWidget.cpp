#include "FilterWidget.h"
#include <QListWidgetItem>

FilterWidget::FilterWidget(QWidget* parent) : QWidget(parent) {
    setupUI();
}

void FilterWidget::setupUI() {
    m_mainLayout = new QVBoxLayout(this);
    
    setupRegionFilter();
    setupRecordTypeFilter();
    setupAirportFilter();
    
    // Statistics label
    m_statsLabel = new QLabel("No data loaded", this);
    m_statsLabel->setStyleSheet("QLabel { font-weight: bold; color: #666; }");
    m_mainLayout->addWidget(m_statsLabel);
    
    m_mainLayout->addStretch();
    setLayout(m_mainLayout);
}

void FilterWidget::setupRegionFilter() {
    m_regionGroup = new QGroupBox("Regions", this);
    QVBoxLayout* layout = new QVBoxLayout(m_regionGroup);
    
    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_selectAllRegionsBtn = new QPushButton("Select All", this);
    m_clearAllRegionsBtn = new QPushButton("Clear All", this);
    buttonLayout->addWidget(m_selectAllRegionsBtn);
    buttonLayout->addWidget(m_clearAllRegionsBtn);
    layout->addLayout(buttonLayout);
    
    // List
    m_regionList = new QListWidget(this);
    m_regionList->setMaximumHeight(120);
    layout->addWidget(m_regionList);
    
    // Connections
    connect(m_selectAllRegionsBtn, &QPushButton::clicked, this, &FilterWidget::onSelectAllRegions);
    connect(m_clearAllRegionsBtn, &QPushButton::clicked, this, &FilterWidget::onClearAllRegions);
    connect(m_regionList, &QListWidget::itemChanged, this, &FilterWidget::onRegionSelectionChanged);
    
    m_mainLayout->addWidget(m_regionGroup);
}

void FilterWidget::setupRecordTypeFilter() {
    m_recordTypeGroup = new QGroupBox("Record Types", this);
    QVBoxLayout* layout = new QVBoxLayout(m_recordTypeGroup);
    
    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_selectAllTypesBtn = new QPushButton("Select All", this);
    m_clearAllTypesBtn = new QPushButton("Clear All", this);
    buttonLayout->addWidget(m_selectAllTypesBtn);
    buttonLayout->addWidget(m_clearAllTypesBtn);
    layout->addLayout(buttonLayout);
    
    // List
    m_recordTypeList = new QListWidget(this);
    m_recordTypeList->setMaximumHeight(150);
    layout->addWidget(m_recordTypeList);
    
    // Connections
    connect(m_selectAllTypesBtn, &QPushButton::clicked, this, &FilterWidget::onSelectAllTypes);
    connect(m_clearAllTypesBtn, &QPushButton::clicked, this, &FilterWidget::onClearAllTypes);
    connect(m_recordTypeList, &QListWidget::itemChanged, this, &FilterWidget::onRecordTypeSelectionChanged);
    
    m_mainLayout->addWidget(m_recordTypeGroup);
}

void FilterWidget::setupAirportFilter() {
    m_airportGroup = new QGroupBox("Airports", this);
    QVBoxLayout* layout = new QVBoxLayout(m_airportGroup);
    
    // Buttons
    QHBoxLayout* buttonLayout = new QHBoxLayout();
    m_selectAllAirportsBtn = new QPushButton("Select All", this);
    m_clearAllAirportsBtn = new QPushButton("Clear All", this);
    buttonLayout->addWidget(m_selectAllAirportsBtn);
    buttonLayout->addWidget(m_clearAllAirportsBtn);
    layout->addLayout(buttonLayout);
    
    // List
    m_airportList = new QListWidget(this);
    m_airportList->setMaximumHeight(120);
    layout->addWidget(m_airportList);
    
    // Connections
    connect(m_selectAllAirportsBtn, &QPushButton::clicked, this, &FilterWidget::onSelectAllAirports);
    connect(m_clearAllAirportsBtn, &QPushButton::clicked, this, &FilterWidget::onClearAllAirports);
    connect(m_airportList, &QListWidget::itemChanged, this, &FilterWidget::onAirportSelectionChanged);
    
    m_mainLayout->addWidget(m_airportGroup);
}

void FilterWidget::setAvailableRegions(const QStringList& regions) {
    m_regionList->clear();
    for (const QString& region : regions) {
        QListWidgetItem* item = new QListWidgetItem(region, m_regionList);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(Qt::Checked);
    }
}

void FilterWidget::setAvailableRecordTypes(const QStringList& types, const QStringList& airports) {
    Q_UNUSED(airports) // For future use if needed
    m_recordTypeList->clear();
    for (const QString& type : types) {
        QListWidgetItem* item = new QListWidgetItem(type, m_recordTypeList);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(Qt::Checked);
    }
}

void FilterWidget::setAvailableAirports(const QStringList& airports, const QStringList& regions) {
    Q_UNUSED(regions) // For future use if needed
    m_airportList->clear();
    for (const QString& airport : airports) {
        QListWidgetItem* item = new QListWidgetItem(airport, m_airportList);
        item->setFlags(item->flags() | Qt::ItemIsUserCheckable);
        item->setCheckState(Qt::Unchecked); // Start with airports unchecked
    }
}

QStringList FilterWidget::getSelectedRegions() const {
    QStringList selected;
    for (int i = 0; i < m_regionList->count(); ++i) {
        QListWidgetItem* item = m_regionList->item(i);
        if (item->checkState() == Qt::Checked) {
            selected.append(item->text());
        }
    }
    return selected;
}

QStringList FilterWidget::getSelectedRecordTypes() const {
    QStringList selected;
    for (int i = 0; i < m_recordTypeList->count(); ++i) {
        QListWidgetItem* item = m_recordTypeList->item(i);
        if (item->checkState() == Qt::Checked) {
            selected.append(item->text());
        }
    }
    return selected;
}

QStringList FilterWidget::getSelectedAirports() const {
    QStringList selected;
    for (int i = 0; i < m_airportList->count(); ++i) {
        QListWidgetItem* item = m_airportList->item(i);
        if (item->checkState() == Qt::Checked) {
            selected.append(item->text());
        }
    }
    return selected;
}

void FilterWidget::clearSelections() {
    for (int i = 0; i < m_regionList->count(); ++i) {
        m_regionList->item(i)->setCheckState(Qt::Unchecked);
    }
    for (int i = 0; i < m_recordTypeList->count(); ++i) {
        m_recordTypeList->item(i)->setCheckState(Qt::Unchecked);
    }
    for (int i = 0; i < m_airportList->count(); ++i) {
        m_airportList->item(i)->setCheckState(Qt::Unchecked);
    }
}

void FilterWidget::onRegionSelectionChanged() {
    // Hierarchical filtering: when regions change, update airports and record types
    emit filtersChanged();
}

void FilterWidget::onRecordTypeSelectionChanged() {
    emit filtersChanged();
}

void FilterWidget::onAirportSelectionChanged() {
    // Hierarchical filtering: when airports change, update record types
    emit filtersChanged();
}

void FilterWidget::onSelectAllRegions() {
    for (int i = 0; i < m_regionList->count(); ++i) {
        m_regionList->item(i)->setCheckState(Qt::Checked);
    }
}

void FilterWidget::onClearAllRegions() {
    for (int i = 0; i < m_regionList->count(); ++i) {
        m_regionList->item(i)->setCheckState(Qt::Unchecked);
    }
}

void FilterWidget::onSelectAllTypes() {
    for (int i = 0; i < m_recordTypeList->count(); ++i) {
        m_recordTypeList->item(i)->setCheckState(Qt::Checked);
    }
}

void FilterWidget::onClearAllTypes() {
    for (int i = 0; i < m_recordTypeList->count(); ++i) {
        m_recordTypeList->item(i)->setCheckState(Qt::Unchecked);
    }
}

void FilterWidget::onSelectAllAirports() {
    for (int i = 0; i < m_airportList->count(); ++i) {
        m_airportList->item(i)->setCheckState(Qt::Checked);
    }
}

void FilterWidget::onClearAllAirports() {
    for (int i = 0; i < m_airportList->count(); ++i) {
        m_airportList->item(i)->setCheckState(Qt::Unchecked);
    }
}
