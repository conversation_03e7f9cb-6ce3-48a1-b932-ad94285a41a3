/**
 * @file PythonInterface.cpp
 * @brief Implementation of Python integration for ARINC 424 parsing
 */

#include "PythonInterface.h"
#include <QDebug>
#include <QDir>
#include <QCoreApplication>
#include <QJsonDocument>
#include <QJsonObject>
#include <QRegularExpression>
#include <stdexcept>

PythonInterface::PythonInterface() 
    : m_initialized(false), m_arincModule(nullptr), m_recordClass(nullptr) {
}

PythonInterface::~PythonInterface() {
    cleanup();
}

bool PythonInterface::initialize() {
    if (m_initialized) {
        return true;
    }

    try {
        if (!initializePython()) {
            throw PythonError("Failed to initialize Python interpreter");
        }

        if (!loadArincModule()) {
            throw PythonError("Failed to load ARINC 424 module");
        }

        m_initialized = true;
        qDebug() << "Python interface initialized successfully";
        return true;

    } catch (const PythonError& e) {
        qCritical() << "Python initialization error:" << e.what();
        cleanup();
        return false;
    } catch (...) {
        qCritical() << "Unknown error during Python initialization";
        cleanup();
        return false;
    }
}

void PythonInterface::cleanup() {
    if (m_recordClass) {
        Py_DECREF(m_recordClass);
        m_recordClass = nullptr;
    }
    if (m_arincModule) {
        Py_DECREF(m_arincModule);
        m_arincModule = nullptr;
    }
    if (m_initialized) {
        Py_Finalize();
        m_initialized = false;
    }
}

bool PythonInterface::initializePython() {
    Py_Initialize();
    if (!Py_IsInitialized()) {
        qCritical() << "Failed to initialize Python interpreter";
        return false;
    }

    // Add current directory and src directory to Python path
    QString appDir = QCoreApplication::applicationDirPath();
    QString srcDir = QDir(appDir).absoluteFilePath("../src");

    // Check if directories exist
    if (!QDir(appDir).exists()) {
        qWarning() << "Application directory does not exist:" << appDir;
    }
    if (!QDir(srcDir).exists()) {
        qWarning() << "Source directory does not exist:" << srcDir;
    }

    // Add paths to Python sys.path
    int result = PyRun_SimpleString("import sys");
    if (result != 0) {
        qCritical() << "Failed to import sys module";
        PyErr_Print();
        return false;
    }

    QString pathCommand1 = QString("sys.path.append('%1')").arg(appDir);
    QString pathCommand2 = QString("sys.path.append('%1')").arg(srcDir);

    if (PyRun_SimpleString(pathCommand1.toUtf8().data()) != 0) {
        qWarning() << "Failed to add app directory to Python path";
        PyErr_Print();
    }

    if (PyRun_SimpleString(pathCommand2.toUtf8().data()) != 0) {
        qWarning() << "Failed to add src directory to Python path";
        PyErr_Print();
    }

    qDebug() << "Python interpreter initialized successfully";
    return true;
}

bool PythonInterface::loadArincModule() {
    // Import the arinc424 module
    m_arincModule = PyImport_ImportModule("arinc424");
    if (!m_arincModule) {
        qCritical() << "Failed to import arinc424 module";
        PyErr_Print();
        return false;
    }

    // Get the Record class
    m_recordClass = PyObject_GetAttrString(m_arincModule, "Record");
    if (!m_recordClass) {
        qCritical() << "Failed to get Record class from arinc424 module";
        PyErr_Print();
        return false;
    }

    // Verify that Record class is callable
    if (!PyCallable_Check(m_recordClass)) {
        qCritical() << "Record class is not callable";
        return false;
    }

    qDebug() << "ARINC 424 module loaded successfully";
    return true;
}

bool PythonInterface::parseArincFile(const QString& filePath, ArincDataModel& dataModel) {
    if (!m_initialized) {
        qDebug() << "Python interface not initialized";
        return false;
    }
    
    dataModel.clearData();
    
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qDebug() << "Cannot open file:" << filePath;
        return false;
    }
    
    QTextStream in(&file);
    int lineCount = 0;
    int parsedCount = 0;
    
    while (!in.atEnd()) {
        QString line = in.readLine();
        lineCount++;
        
        // Skip header lines
        if (line.startsWith("HDR")) {
            continue;
        }
        
        ArincRecord record = parseArincRecord(line);
        if (record.isValid()) {
            dataModel.addRecord(record);
            parsedCount++;
        }
        
        // Progress feedback for large files
        if (lineCount % 1000 == 0) {
            qDebug() << "Processed" << lineCount << "lines, parsed" << parsedCount << "records";
        }
    }
    
    qDebug() << "Finished parsing. Total lines:" << lineCount << "Parsed records:" << parsedCount;
    return true;
}

ArincRecord PythonInterface::parseArincRecord(const QString& recordLine) {
    ArincRecord record;
    
    if (!m_initialized || !m_recordClass) {
        return record;
    }
    
    // Create a new Record instance
    PyObject* recordInstance = PyObject_CallObject(m_recordClass, nullptr);
    if (!recordInstance) {
        PyErr_Print();
        return record;
    }
    
    // Call the read method
    PyObject* readMethod = PyObject_GetAttrString(recordInstance, "read");
    if (!readMethod) {
        Py_DECREF(recordInstance);
        return record;
    }
    
    PyObject* args = PyTuple_New(1);
    PyTuple_SetItem(args, 0, PyUnicode_FromString(recordLine.toUtf8().data()));
    
    PyObject* readResult = PyObject_CallObject(readMethod, args);
    Py_DECREF(args);
    Py_DECREF(readMethod);
    
    if (!readResult || !PyBool_Check(readResult) || !PyObject_IsTrue(readResult)) {
        Py_XDECREF(readResult);
        Py_DECREF(recordInstance);
        return record; // Failed to parse
    }
    Py_DECREF(readResult);
    
    // Get JSON representation
    PyObject* jsonMethod = PyObject_GetAttrString(recordInstance, "json");
    if (!jsonMethod) {
        Py_DECREF(recordInstance);
        return record;
    }
    
    // Call json method with output=False
    PyObject* jsonArgs = PyTuple_New(1);
    PyTuple_SetItem(jsonArgs, 0, Py_False);
    Py_INCREF(Py_False);
    
    PyObject* jsonResult = PyObject_CallObject(jsonMethod, jsonArgs);
    Py_DECREF(jsonArgs);
    Py_DECREF(jsonMethod);
    
    if (!jsonResult) {
        Py_DECREF(recordInstance);
        return record;
    }
    
    QString jsonString = pythonStringToQString(jsonResult);
    Py_DECREF(jsonResult);
    Py_DECREF(recordInstance);
    
    // Parse JSON to extract record data
    QJsonDocument doc = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!doc.isObject()) {
        return record;
    }
    
    QJsonObject obj = doc.object();
    
    // Extract basic information
    record.sectionCode = obj["Section Code"].toString().trimmed();
    record.customerAreaCode = obj["Customer / Area Code"].toString().trimmed();

    // Skip records with empty section codes
    if (record.sectionCode.isEmpty()) {
        record.type = RecordType::UNKNOWN;
        return record;
    }

    // Determine record type - handle special case for SID/STAR/Approach procedures
    // These might have section code "PA" but are actually procedures
    record.type = parseRecordType(record.sectionCode);

    // Debug: Log unknown section codes
    static QSet<QString> unknownCodes;
    if (record.type == RecordType::UNKNOWN && !unknownCodes.contains(record.sectionCode)) {
        qDebug() << "Unknown section code:" << record.sectionCode;
        unknownCodes.insert(record.sectionCode);
    }

    // Check if this is actually a SID/STAR/Approach procedure
    if (obj.contains("SID/STAR/Approach Identifier")) {
        QString procedureId = obj["SID/STAR/Approach Identifier"].toString().trimmed();
        QString routeType = obj.value("Route Type").toString();

        if (!procedureId.isEmpty()) {
            // Use the section code from the Python library to determine type
            if (record.sectionCode == "PD") {
                record.type = RecordType::SID;
            } else if (record.sectionCode == "PE") {
                record.type = RecordType::STAR;
            } else if (record.sectionCode == "PF") {
                record.type = RecordType::APPROACH;
            } else {
                // Fallback: determine by route type for PA records that are actually procedures
                if (routeType == "1" || routeType == "2" || routeType == "3") {
                    record.type = RecordType::STAR;  // STAR route types
                } else if (routeType == "0") {
                    record.type = RecordType::SID;   // SID route types
                } else {
                    record.type = RecordType::APPROACH;
                }
            }
            record.procedureIdentifier = procedureId;
            record.routeType = routeType;
        }
    }
    
    // Extract identifier and name
    if (obj.contains("VOR Identifier")) {
        record.identifier = obj["VOR Identifier"].toString().trimmed();
    } else if (obj.contains("Waypoint Identifier")) {
        record.identifier = obj["Waypoint Identifier"].toString().trimmed();
    } else if (obj.contains("Airport Identifier")) {
        record.identifier = obj["Airport Identifier"].toString().trimmed();
    } else if (obj.contains("Fix Identifier")) {
        record.identifier = obj["Fix Identifier"].toString().trimmed();
    }

    // Extract sequence number for procedures
    if (obj.contains("Sequence Number")) {
        QString seqStr = obj["Sequence Number"].toString().trimmed();
        bool ok;
        int seq = seqStr.toInt(&ok);
        if (ok) {
            record.sequenceNumber = seq;
        }
    }
    
    if (obj.contains("VOR Name")) {
        record.name = obj["VOR Name"].toString().trimmed();
    } else if (obj.contains("Airport Name")) {
        record.name = obj["Airport Name"].toString().trimmed();
    }
    
    // Extract coordinates - try multiple field name patterns
    QString latStr, lonStr;

    // Try all possible latitude/longitude field combinations
    QStringList latFields = {"DME Latitude", "VOR Latitude", "Waypoint Latitude",
                            "Airport Reference Pt. Latitude", "Runway Latitude",
                            "Localizer Latitude", "Glideslope Latitude", "Marker Latitude"};
    QStringList lonFields = {"DME Longitude", "VOR Longitude", "Waypoint Longitude",
                            "Airport Reference Pt. Longitude", "Runway Longitude",
                            "Localizer Longitude", "Glideslope Longitude", "Marker Longitude"};

    for (int i = 0; i < latFields.size() && latStr.isEmpty(); ++i) {
        if (obj.contains(latFields[i]) && obj.contains(lonFields[i])) {
            latStr = obj[latFields[i]].toString();
            lonStr = obj[lonFields[i]].toString();
        }
    }
    
    if (!latStr.isEmpty() && !lonStr.isEmpty()) {
        record.coordinate = parseCoordinate(latStr, lonStr);
    }
    
    // Store all additional data
    for (auto it = obj.begin(); it != obj.end(); ++it) {
        record.additionalData[it.key()] = it.value().toVariant();
    }



    return record;
}

QGeoCoordinate PythonInterface::parseCoordinate(const QString& latStr, const QString& lonStr) {
    if (latStr.isEmpty() || lonStr.isEmpty() || latStr.trimmed().isEmpty() || lonStr.trimmed().isEmpty()) {
        return QGeoCoordinate();
    }

    double lat = parseLatitude(latStr);
    double lon = parseLongitude(lonStr);

    if (qIsNaN(lat) || qIsNaN(lon)) {
        return QGeoCoordinate();
    }

    return QGeoCoordinate(lat, lon);
}

double PythonInterface::parseLatitude(const QString& latStr) {
    // Format: N47265700 or S47265700 (degrees, minutes, seconds * 100)
    if (latStr.length() < 9) return qQNaN();

    QChar hemisphere = latStr[0];
    QString coords = latStr.mid(1);

    if (coords.length() != 8) return qQNaN();

    bool ok;
    int degrees = coords.mid(0, 2).toInt(&ok);
    if (!ok) return qQNaN();

    int minutes = coords.mid(2, 2).toInt(&ok);
    if (!ok) return qQNaN();

    int seconds = coords.mid(4, 4).toInt(&ok);
    if (!ok) return qQNaN();

    double lat = degrees + minutes / 60.0 + (seconds / 100.0) / 3600.0;

    if (hemisphere == 'S') lat = -lat;

    return lat;
}

double PythonInterface::parseLongitude(const QString& lonStr) {
    // Format: W122182910 or E122182910 (degrees, minutes, seconds * 100)
    if (lonStr.length() < 10) return qQNaN();

    QChar hemisphere = lonStr[0];
    QString coords = lonStr.mid(1);

    if (coords.length() != 9) return qQNaN();

    bool ok;
    int degrees = coords.mid(0, 3).toInt(&ok);
    if (!ok) return qQNaN();

    int minutes = coords.mid(3, 2).toInt(&ok);
    if (!ok) return qQNaN();

    int seconds = coords.mid(5, 4).toInt(&ok);
    if (!ok) return qQNaN();

    double lon = degrees + minutes / 60.0 + (seconds / 100.0) / 3600.0;

    if (hemisphere == 'W') lon = -lon;

    return lon;
}

QString PythonInterface::pythonStringToQString(PyObject* pyStr) {
    if (!pyStr || !PyUnicode_Check(pyStr)) {
        return QString();
    }

    const char* utf8 = PyUnicode_AsUTF8(pyStr);
    if (!utf8) {
        return QString();
    }

    return QString::fromUtf8(utf8);
}
