/**
 * @file PythonInterface.h
 * @brief Interface for integrating Python ARINC 424 parsing with C++ application
 *
 * This file provides a C++ wrapper around Python-based ARINC 424 parsing functionality.
 */

#pragma once

// Prevent Qt's slots macro from conflicting with Python headers
#ifdef slots
#undef slots
#endif
#include <Python.h>
#define slots Q_SLOTS
#include <QString>
#include <QVector>
#include <QVariantMap>
#include <QGeoCoordinate>
#include <stdexcept>
#include "DataModels.h"

/**
 * @class PythonError
 * @brief Exception class for Python-related errors
 */
class PythonError : public std::runtime_error {
public:
    explicit PythonError(const std::string& message) : std::runtime_error(message) {}
};

/**
 * @class PythonInterface
 * @brief Interface for Python-based ARINC 424 data parsing
 *
 * This class provides a C++ interface to Python-based ARINC 424 parsing
 * functionality, handling Python initialization, module loading, and data conversion.
 */
class PythonInterface {
public:
    /**
     * @brief Constructor
     */
    PythonInterface();

    /**
     * @brief Destructor - automatically cleans up Python resources
     */
    ~PythonInterface();

    /**
     * @brief Initialize the Python interpreter and load required modules
     * @return true if initialization successful, false otherwise
     * @throws PythonError if initialization fails
     */
    bool initialize();

    /**
     * @brief Clean up Python resources
     */
    void cleanup();

    /**
     * @brief Parse an ARINC 424 file using Python library
     * @param filePath Path to the ARINC 424 file
     * @param dataModel Data model to populate with parsed records
     * @return true if parsing successful, false otherwise
     * @throws PythonError if parsing fails
     */
    bool parseArincFile(const QString& filePath, ArincDataModel& dataModel);

    /**
     * @brief Parse a single ARINC 424 record line
     * @param recordLine The record line to parse
     * @return Parsed ArincRecord object
     * @throws PythonError if parsing fails
     */
    ArincRecord parseArincRecord(const QString& recordLine);
    
private:
    bool m_initialized;
    PyObject* m_arincModule;
    PyObject* m_recordClass;
    
    // Helper methods
    bool initializePython();
    bool loadArincModule();
    QGeoCoordinate parseCoordinate(const QString& latStr, const QString& lonStr);
    QVariantMap pythonDictToQVariantMap(PyObject* dict);
    QString pythonStringToQString(PyObject* pyStr);
    double parseLatitude(const QString& latStr);
    double parseLongitude(const QString& lonStr);
};
