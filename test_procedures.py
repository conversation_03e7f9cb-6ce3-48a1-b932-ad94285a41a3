#!/usr/bin/env python3
"""
Test script to verify SID/STAR procedure detection and parsing
"""

import sys
import os

# Add src directory to path
sys.path.insert(0, '../src')

try:
    import arinc424
    print("✓ ARINC 424 library imported successfully")
except ImportError as e:
    print(f"✗ Failed to import ARINC 424 library: {e}")
    sys.exit(1)

def test_star_records():
    """Test parsing STAR records from the example file"""
    
    print("\nTesting STAR record parsing...")
    
    # Sample STAR records from the data/ARINC-424-18/stars file
    star_records = [
        "SUSAP KSEAK1EELN2  1GEG   010GEG  K1D 1V       IF                                         18000                            046328508",
        "SUSAP KSEAK1EELN2  1GEG   020HAMURK1EA1E       TF                                                                          046348508",
        "SUSAP KSEAK1EELN2  1GEG   030ELN  K1D 1VE H    TF                                                                          046368508",
        "SUSAP KSEAK1EELN2  1HAMUR 010HAMURK1EA1E       IF                                         18000                            046388508",
        "SUSAP KSEAK1EELN2  1HAMUR 020ELN  K1D 1VE H    TF                                                                          046408508"
    ]
    
    parsed_procedures = []
    
    for i, record_line in enumerate(star_records, 1):
        print(f"\nSTAR Record {i}:")
        print(f"Raw: {record_line[:80]}...")
        
        try:
            record = arinc424.Record()
            if record.read(record_line):
                # Get JSON data
                json_data = record.json(output=False)
                print(f"✓ Parsed successfully")
                print(f"  Section: {record.ident}")
                print(f"  Definition: {record.definition.name}")
                
                # Parse JSON to extract key info
                import json
                data = json.loads(json_data)
                
                if "Airport Identifier" in data:
                    print(f"  Airport: {data['Airport Identifier']}")
                
                if "SID/STAR/Approach Identifier" in data:
                    print(f"  Procedure: {data['SID/STAR/Approach Identifier']}")
                
                if "Sequence Number" in data:
                    print(f"  Sequence: {data['Sequence Number']}")
                
                if "Fix Identifier" in data:
                    print(f"  Fix: {data['Fix Identifier']}")
                
                if "Route Type" in data:
                    print(f"  Route Type: {data['Route Type']}")
                
                parsed_procedures.append(data)
                
            else:
                print(f"✗ Failed to parse")
                
        except Exception as e:
            print(f"✗ Error parsing: {e}")
    
    print(f"\nSummary: {len(parsed_procedures)}/{len(star_records)} STAR records parsed successfully")
    
    # Group by procedure and sequence
    procedures = {}
    for proc in parsed_procedures:
        proc_id = proc.get("SID/STAR/Approach Identifier", "").strip()
        airport = proc.get("Airport Identifier", "").strip()
        seq = proc.get("Sequence Number", "").strip()
        fix = proc.get("Fix Identifier", "").strip()
        
        if proc_id and airport:
            key = f"{airport}_{proc_id}"
            if key not in procedures:
                procedures[key] = []
            procedures[key].append({
                'sequence': seq,
                'fix': fix,
                'data': proc
            })
    
    print(f"\nFound {len(procedures)} unique procedures:")
    for proc_key, waypoints in procedures.items():
        print(f"  {proc_key}: {len(waypoints)} waypoints")
        # Sort by sequence number
        waypoints.sort(key=lambda x: int(x['sequence']) if x['sequence'].isdigit() else 999)
        for wp in waypoints:
            print(f"    Seq {wp['sequence']}: {wp['fix']}")
    
    return len(parsed_procedures) > 0

def test_dual_identifier_system():
    """Test the dual identifier system for detecting procedures"""
    
    print("\nTesting dual identifier system...")
    
    # Test record that should be detected as PE (STAR) via dual identifier
    test_record = "SUSAP KSEAK1EELN2  1GEG   010GEG  K1D 1V       IF                                         18000                            046328508"
    
    print(f"Test record: {test_record[:60]}...")
    print(f"Position 4:6 (identifier_1): '{test_record[4:6]}'")
    print(f"Position 4 + 12 (identifier_2): '{test_record[4]}{test_record[12]}'")
    
    try:
        record = arinc424.Record()
        if record.read(test_record):
            print(f"✓ Record parsed with identifier: {record.ident}")
            print(f"  Definition: {record.definition.name}")
            
            # Check if it's detected as a procedure
            json_data = record.json(output=False)
            import json
            data = json.loads(json_data)
            
            has_procedure_id = "SID/STAR/Approach Identifier" in data and data["SID/STAR/Approach Identifier"].strip()
            print(f"  Has procedure identifier: {has_procedure_id}")
            
            if has_procedure_id:
                print(f"  Procedure ID: {data['SID/STAR/Approach Identifier']}")
                
            return True
        else:
            print("✗ Failed to parse test record")
            return False
            
    except Exception as e:
        print(f"✗ Error: {e}")
        return False

def main():
    print("ARINC 424 Procedure Detection Test")
    print("=" * 50)
    
    # Test 1: STAR record parsing
    test1_passed = test_star_records()
    
    # Test 2: Dual identifier system
    test2_passed = test_dual_identifier_system()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"STAR record parsing: {'✓ PASS' if test1_passed else '✗ FAIL'}")
    print(f"Dual identifier system: {'✓ PASS' if test2_passed else '✗ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 All procedure tests passed!")
        print("\nThe C++ application should now properly detect and visualize:")
        print("  • SID procedures (orange)")
        print("  • STAR procedures (green)")  
        print("  • Approach procedures (red)")
        print("  • Connected waypoint sequences")
        print("  • Hierarchical filtering by region → airport → record type")
    else:
        print("\n❌ Some procedure tests failed.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
