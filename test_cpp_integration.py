#!/usr/bin/env python3
"""
Test script to verify the C++ application can detect SID/STAR procedures
"""

import sys
import os
import json

# Add src directory to path
sys.path.insert(0, 'src')

try:
    import arinc424
    print("✓ ARINC 424 library imported successfully")
except ImportError as e:
    print(f"✗ Failed to import ARINC 424 library: {e}")
    sys.exit(1)

def test_procedure_detection_in_flyright():
    """Test if we can find SID/STAR procedures in the flyright.pc file"""
    
    print("\nSearching for SID/STAR procedures in flyright.pc...")
    
    if not os.path.exists("flyright.pc"):
        print("✗ flyright.pc file not found")
        return False
    
    procedures_found = []
    record_count = 0
    
    with open("flyright.pc", "r") as f:
        for line_num, line in enumerate(f, 1):
            if line.startswith("HDR"):
                continue
                
            record_count += 1
            
            try:
                record = arinc424.Record()
                if record.read(line):
                    # Get JSON data
                    json_data = record.json(output=False)
                    data = json.loads(json_data)
                    
                    # Check if this is a procedure record
                    has_procedure_id = "SID/STAR/Approach Identifier" in data and data["SID/STAR/Approach Identifier"].strip()
                    
                    if has_procedure_id:
                        procedure_info = {
                            'line': line_num,
                            'section': record.ident,
                            'airport': data.get("Airport Identifier", "").strip(),
                            'procedure_id': data.get("SID/STAR/Approach Identifier", "").strip(),
                            'sequence': data.get("Sequence Number", "").strip(),
                            'fix': data.get("Fix Identifier", "").strip(),
                            'route_type': data.get("Route Type", "").strip()
                        }
                        procedures_found.append(procedure_info)
                        
                        # Print first few for verification
                        if len(procedures_found) <= 5:
                            print(f"  Found procedure: {procedure_info['airport']} {procedure_info['procedure_id']} seq {procedure_info['sequence']} fix {procedure_info['fix']}")
                
            except Exception as e:
                pass  # Skip problematic records
            
            # Limit search to first 50k records for speed
            if record_count >= 50000:
                break
    
    print(f"\nResults:")
    print(f"  Records searched: {record_count}")
    print(f"  Procedures found: {len(procedures_found)}")
    
    if procedures_found:
        # Group by airport and procedure
        grouped = {}
        for proc in procedures_found:
            key = f"{proc['airport']}_{proc['procedure_id']}"
            if key not in grouped:
                grouped[key] = []
            grouped[key].append(proc)
        
        print(f"  Unique procedures: {len(grouped)}")
        print(f"  Sample procedures:")
        for i, (proc_key, waypoints) in enumerate(list(grouped.items())[:3]):
            print(f"    {proc_key}: {len(waypoints)} waypoints")
        
        return True
    else:
        print("  ✗ No procedures found")
        return False

def test_section_code_detection():
    """Test the dual identifier system detection"""
    
    print("\nTesting section code detection...")
    
    # Test records that should be detected as procedures
    test_cases = [
        # This should be detected as PE (STAR) via dual identifier
        ("SUSAP KSEAK1EELN2  1GEG   010GEG  K1D 1V       IF                                         18000                            046328508", "PE", "STAR"),
        # Regular airport record
        ("SCANP CYOWCYAYOW     010000100Y N45192101W075400217W014000377250YOW CY1800018000CU00Y NAS    OTTAWA MACDONALD-CARTIER INTL     031691204", "PA", "Airport"),
    ]
    
    for test_line, expected_section, expected_type in test_cases:
        print(f"\nTesting: {test_line[:50]}...")
        print(f"  Expected section: {expected_section}")
        
        try:
            record = arinc424.Record()
            if record.read(test_line):
                print(f"  ✓ Detected section: {record.ident}")
                print(f"  ✓ Definition: {record.definition.name}")
                
                # Check if it matches expectation
                if record.ident == expected_section:
                    print(f"  ✓ Correct detection!")
                else:
                    print(f"  ✗ Expected {expected_section}, got {record.ident}")
                    
            else:
                print(f"  ✗ Failed to parse")
                
        except Exception as e:
            print(f"  ✗ Error: {e}")
    
    return True

def main():
    print("C++ Integration Test - SID/STAR Detection")
    print("=" * 50)
    
    # Test 1: Section code detection
    test1_passed = test_section_code_detection()
    
    # Test 2: Find procedures in actual data
    test2_passed = test_procedure_detection_in_flyright()
    
    print("\n" + "=" * 50)
    print("Test Results:")
    print(f"Section code detection: {'✓ PASS' if test1_passed else '✗ FAIL'}")
    print(f"Procedure detection: {'✓ PASS' if test2_passed else '✗ FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n🎉 Integration tests passed!")
        print("\nThe C++ application should now show:")
        print("  • SID, STAR, Approach in the record type filter")
        print("  • Orange lines for SID procedures")
        print("  • Green lines for STAR procedures")
        print("  • Red lines for Approach procedures")
        print("  • Hierarchical filtering: Region → Airport → Record Type")
    else:
        print("\n❌ Some integration tests failed.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
