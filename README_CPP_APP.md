# ARINC 424 Viewer - C++ Application

A C++ application that uses the Python ARINC 424 library to parse aviation navigation data and display it on an interactive map with filtering capabilities.

## Features

- **ARINC 424 File Parsing**: Uses the existing Python library to parse ARINC 424 files
- **Interactive Map**: Leaflet-based map showing aviation navigation data
- **Filtering System**: Filter data by:
  - Region (CAN, USA, etc.)
  - Record Type (VHF Navaid, Waypoint, Airport, etc.)
  - Airport codes
- **Data Visualization**: Different colors and markers for different record types
- **Procedure Paths**: Connect related waypoints for approaches and departures
- **Modern UI**: Qt6-based interface with dark theme

## Architecture

The application consists of several key components:

### Core Components

1. **PythonInterface**: Embeds Python interpreter and calls the ARINC 424 library
2. **DataModels**: C++ data structures for ARINC records and filtering
3. **MapWidget**: Web-based map using Qt WebEngine and Leaflet
4. **FilterWidget**: UI controls for filtering data
5. **MainWindow**: Main application window coordinating all components

### Data Flow

```
ARINC 424 File → Python Parser → C++ Data Model → Filters → Map Display
```

## Prerequisites

### System Dependencies

- **Qt6**: Core, Widgets, WebEngineWidgets
- **Python 3.10+**: With development headers
- **CMake 3.16+**
- **C++17 compatible compiler**

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install qt6-base-dev qt6-webengine-dev python3-dev cmake build-essential
```

### Fedora
```bash
sudo dnf install qt6-qtbase-devel qt6-qtwebengine-devel python3-devel cmake gcc-c++
```

### Arch Linux
```bash
sudo pacman -S qt6-base qt6-webengine python cmake gcc
```

### Python Dependencies

The application requires the ARINC 424 Python library to be installed:

```bash
# Install the library from the current directory
pip install -e .
```

## Building

1. **Clone/Navigate to the repository**:
   ```bash
   cd /path/to/arinc424-main
   ```

2. **Run the build script**:
   ```bash
   ./build.sh
   ```

   Or build manually:
   ```bash
   mkdir build && cd build
   cmake .. -DCMAKE_BUILD_TYPE=Release
   make -j$(nproc)
   ```

## Running

```bash
cd build
./arinc424_viewer
```

## Usage

1. **Open File**: Click "Open ARINC 424 File" or use File → Open to load a `.pc` file
2. **Filter Data**: Use the left panel to filter by region, record type, or airport
3. **View Map**: The map will automatically update to show filtered data
4. **Interact**: Click on markers to see detailed information

### Supported Record Types

- **VHF Navaid** (D): VOR/DME navigation aids
- **NDB Navaid** (DB): Non-directional beacons  
- **Waypoint** (EA): Enroute waypoints
- **Airways Marker** (EM): Airway intersection markers
- **Holding Pattern** (EP): Published holding patterns
- **Enroute Airways** (ER): Airway definitions
- **Preferred Route** (ET): Preferred routing
- **Airport** (PA): Airport reference points
- **Controlled Airspace** (UC): Controlled airspace boundaries
- **Restrictive Airspace** (UR): Restricted/prohibited areas

### Map Features

- **Color Coding**: Different colors for each record type
- **Zoom/Pan**: Standard map navigation
- **Popups**: Click markers for detailed information
- **Auto-fit**: Map automatically centers on filtered data

## File Structure

```
├── CMakeLists.txt          # Build configuration
├── build.sh               # Build script
├── src/                   # C++ source code
│   ├── main.cpp          # Application entry point
│   ├── MainWindow.*      # Main application window
│   ├── DataModels.*      # Data structures and filtering
│   ├── PythonInterface.* # Python integration
│   ├── MapWidget.*       # Map display component
│   └── FilterWidget.*    # Filter controls
├── python/               # Python wrapper scripts
│   └── arinc_parser.py  # Python interface script
└── README_CPP_APP.md    # This file
```

## Troubleshooting

### Build Issues

1. **Qt6 not found**: Make sure Qt6 development packages are installed
2. **Python headers missing**: Install python3-dev/python3-devel package
3. **CMake errors**: Ensure CMake 3.16+ is installed

### Runtime Issues

1. **Python import errors**: Verify ARINC 424 library is installed (`pip install -e .`)
2. **Map not loading**: Check internet connection (map tiles loaded from OpenStreetMap)
3. **File parsing errors**: Ensure file is valid ARINC 424 format

### Performance

- Large files (>100k records) may take time to load
- Consider filtering data to improve map performance
- Use "Clear All" filters to hide data temporarily

## Development

### Adding New Record Types

1. Add enum value to `RecordType` in `DataModels.h`
2. Update `parseRecordType()` function in `DataModels.cpp`
3. Add color mapping in `MapWidget::getRecordColor()`
4. Update `recordTypeToString()` for display names

### Extending Filters

1. Add new filter controls to `FilterWidget`
2. Update `getFilteredRecords()` in `DataModels`
3. Connect new filters to `onFiltersChanged()` signal

### Map Customization

1. Modify `generateMapHTML()` in `MapWidget.cpp`
2. Update Leaflet configuration and styling
3. Add custom markers or overlays as needed

## License

This C++ application follows the same license as the original ARINC 424 Python library.
