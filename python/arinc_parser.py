#!/usr/bin/env python3
"""
Python wrapper for ARINC 424 parsing to be used by the C++ application.

This script provides a simplified interface to the arinc424 library,
handling file parsing, record validation, and data conversion.

Author: ARINC 424 Viewer Team
Version: 1.0
"""

import sys
import os
import json
import logging
from typing import Optional, Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stderr)]
)
logger = logging.getLogger(__name__)

# Add the src directory to the path to import arinc424
current_dir = os.path.dirname(os.path.abspath(__file__))
src_dir = os.path.join(os.path.dirname(current_dir), 'src')
sys.path.insert(0, src_dir)

try:
    import arinc424
    logger.info("Successfully imported arinc424 module")
except ImportError as e:
    logger.error(f"Error importing arinc424: {e}")
    print(f"Error importing arinc424: {e}", file=sys.stderr)
    sys.exit(1)

def parse_record(record_line: str) -> Optional[Dict[str, Any]]:
    """
    Parse a single ARINC 424 record line and return JSON data.

    Args:
        record_line: The ARINC 424 record line to parse

    Returns:
        Parsed record data as dictionary, or None if parsing failed

    Raises:
        ValueError: If record line is empty or invalid
    """
    if not record_line or not record_line.strip():
        logger.warning("Empty record line provided")
        return None

    try:
        record = arinc424.Record()
        if record.read(record_line.strip()):
            # Get JSON representation
            json_str = record.json(output=False)
            parsed_data = json.loads(json_str)
            logger.debug(f"Successfully parsed record: {record_line[:20]}...")
            return parsed_data
        else:
            logger.warning(f"Failed to parse record: {record_line[:50]}...")
            return None
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error for record: {e}")
        return None
    except Exception as e:
        logger.error(f"Error parsing record: {e}")
        return None

def parse_file(file_path: str) -> list:
    """
    Parse an entire ARINC 424 file and return all records as JSON.

    Args:
        file_path: Path to the ARINC 424 file to parse

    Returns:
        List of parsed record dictionaries

    Raises:
        FileNotFoundError: If the specified file does not exist
        PermissionError: If the file cannot be read due to permissions
        UnicodeDecodeError: If the file contains invalid characters
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"File not found: {file_path}")

    if not os.access(file_path, os.R_OK):
        raise PermissionError(f"File not readable: {file_path}")

    records = []
    logger.info(f"Starting to parse file: {file_path}")

    try:
        with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
            line_count = 0
            skipped_count = 0

            for line in f:
                line_count += 1

                # Skip header lines and empty lines
                if line.startswith('HDR') or not line.strip():
                    skipped_count += 1
                    continue

                record_data = parse_record(line)
                if record_data:
                    records.append(record_data)

                # Progress feedback for large files
                if line_count % 1000 == 0:
                    logger.info(f"Processed {line_count} lines, parsed {len(records)} records, skipped {skipped_count}")

            logger.info(f"File parsing completed: {len(records)} records parsed from {line_count} lines")

    except UnicodeDecodeError as e:
        logger.error(f"Unicode decode error reading file {file_path}: {e}")
        raise
    except IOError as e:
        logger.error(f"IO error reading file {file_path}: {e}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error reading file {file_path}: {e}")
        raise

    return records

def get_record_types():
    """
    Get available record types from the arinc424 library.
    
    Returns:
        dict: Mapping of section codes to record type names
    """
    # This would ideally come from the library, but we'll hardcode the known types
    return {
        'D ': 'VHF Navaid',
        'DB': 'NDB Navaid', 
        'EA': 'Waypoint',
        'EM': 'Airways Marker',
        'EP': 'Holding Pattern',
        'ER': 'Enroute Airways',
        'ET': 'Preferred Route',
        'PA': 'Airport',
        'PB': 'Airport Gate',
        'PC': 'Terminal Waypoint',
        'PD': 'SID',
        'PE': 'STAR', 
        'PF': 'Approach',
        'PG': 'Runway',
        'PI': 'Localizer/Glideslope',
        'PK': 'TAA',
        'PL': 'MLS',
        'PM': 'Localizer Marker',
        'PN': 'NDB Navaid',
        'PP': 'Path Point',
        'PR': 'Flight Planning',
        'PS': 'MSA',
        'PT': 'GLS',
        'PV': 'Airport Communication',
        'R ': 'Company Route',
        'RA': 'Alternate',
        'TC': 'Cruising Tables',
        'TG': 'Geo Reference Table',
        'UC': 'Controlled Airspace',
        'UF': 'FIR/UIR',
        'UR': 'Restrictive Airspace'
    }

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python arinc_parser.py <command> [args...]")
        print("Commands:")
        print("  parse_file <file_path>    - Parse entire file and output JSON")
        print("  parse_record <record>     - Parse single record and output JSON")
        print("  get_types                 - Get available record types")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "parse_file":
        if len(sys.argv) < 3:
            print("Error: file_path required", file=sys.stderr)
            sys.exit(1)
        
        file_path = sys.argv[2]
        records = parse_file(file_path)
        print(json.dumps(records, indent=2))
        
    elif command == "parse_record":
        if len(sys.argv) < 3:
            print("Error: record line required", file=sys.stderr)
            sys.exit(1)
        
        record_line = sys.argv[2]
        record_data = parse_record(record_line)
        if record_data:
            print(json.dumps(record_data, indent=2))
        else:
            print("Failed to parse record", file=sys.stderr)
            sys.exit(1)
            
    elif command == "get_types":
        types = get_record_types()
        print(json.dumps(types, indent=2))
        
    else:
        print(f"Unknown command: {command}", file=sys.stderr)
        sys.exit(1)
